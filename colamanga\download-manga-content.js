const { chromium } = require('playwright');
const fs = require('fs-extra');
const path = require('path');

class MangaContentDownloader {
    constructor() {
        this.browser = null;
        this.page = null;
        this.outputDir = 'E:\\manga';
    }

    async init() {
        console.log('🚀 启动浏览器...');
        
        // 方案1: 使用普通启动 + 扩展
        this.browser = await chromium.launch({
            headless: false,
            channel: 'chrome',
            args: [
                // '--load-extension=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Extensions\\',
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ],
            timeout: 300000
        });
        
        this.context = await this.browser.newContext();
        this.page = await this.context.newPage();
        
        // 设置用户代理
        await this.page.setExtraHTTPHeaders({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });

        // 监听浏览器控制台消息
        this.page.on('console', msg => {
            // console.log(`🖥️ 浏览器控制台: ${msg.text()}`);
        });
        
        // 确保输出目录存在
        await fs.ensureDir(this.outputDir);

        // 设置 blob 图片捕获
        await this.setupBlobCapture();
    }

    async setupBlobCapture() {
        // 简化的blob URL监听，仅用于调试
        await this.page.addInitScript(() => {
            const originalCreateObjectURL = URL.createObjectURL;
            URL.createObjectURL = function(object) {
                const blobUrl = originalCreateObjectURL.call(this, object);
                // 将 blob URL 信息传递给页面，用于调试
                window.__blobUrls = window.__blobUrls || [];
                window.__blobUrls.push({
                    blobUrl: blobUrl,
                    size: object.size,
                    type: object.type,
                    timestamp: Date.now()
                });
                console.log('🔗 创建blob URL:', blobUrl, 'size:', object.size);
                return blobUrl;
            };
        });
    }

    async getChapterTitle() {
        try {
            return await this.page.evaluate(() => {
                const titleElement = document.querySelector('.mh_readtitle');
                console.log('🔍 查找标题元素:', titleElement ? '找到' : '未找到');

                if (titleElement) {
                    let title = titleElement.textContent.trim();
                    console.log('📝 原始标题:', title);

                    // 清理标题，移除导航文本
                    title = title.replace(/返回目录/g, '');
                    title = title.replace(/返回首页/g, '');
                    title = title.replace(/上一章/g, '');
                    title = title.replace(/下一章/g, '');
                    title = title.replace(/\s+/g, ' '); // 合并多个空格
                    title = title.trim();

                    console.log('🧹 清理后标题:', title);
                    return title || null;
                }
                return null;
            });
        } catch (error) {
            console.log('⚠️ 无法获取章节标题:', error.message);
            return null;
        }
    }

    async downloadMangaContent(mangaId, mangaName, chapter = 1) {
        console.log(`📖 开始下载漫画: ${mangaName} (ID: ${mangaId}), 章节: ${chapter}`);

        // 创建漫画目录
        const mangaDir = path.join(this.outputDir, this.sanitizeFileName(mangaName));
        await fs.ensureDir(mangaDir);

        // 重构的章节导航逻辑
        const navigationResult = await this.navigateToChapter(mangaId, chapter);
        if (!navigationResult.success) {
            console.log(`📄 章节 ${chapter} 不存在或无法访问`);
            return false;
        }

        // 获取章节标题
        const chapterTitle = navigationResult.title;
        console.log(`📝 章节标题: ${chapterTitle || '未获取到标题'}`);

        // 确保每个章节都有唯一的目录名，即使标题相同或为空
        const chapterDirName = chapterTitle ?
            `第${chapter}章-${this.sanitizeFileName(chapterTitle)}` :
            `第${chapter}章`;

        const chapterDir = path.join(mangaDir, chapterDirName);
        await fs.ensureDir(chapterDir);

        console.log(`📁 章节目录: ${chapterDirName}`);
        console.log(`📂 完整路径: ${chapterDir}`);

        // 智能检测章节完整性
        const chapterStatus = await this.analyzeChapterCompleteness(chapterDir);

        if (chapterStatus.isComplete) {
            console.log(`✅ 章节已完整下载，跳过重复下载`);
            return true;
        } else if (chapterStatus.hasPartialContent) {
            console.log(`📊 发现部分内容，将进行增量下载`);
            return await this.performIncrementalDownload(chapterDir, chapterStatus);
        } else {
            console.log(`🆕 开始全新下载章节`);
            return await this.performFullDownload(chapterDir);
        }
    }

    /**
     * 重构的章节导航逻辑
     * 根据 JSON 配置文件中的章节 ID，按顺序从第一章开始进入指定的章节网页
     */
    async navigateToChapter(mangaId, chapter) {
        console.log(`🧭 开始导航到章节: ${chapter}`);

        // 构建章节 URL - 确保正确解析章节 URL
        const chapterUrl = `https://www.colamanga.com/manga-${mangaId}/1/${chapter}.html`;
        console.log(`🔗 访问章节 URL: ${chapterUrl}`);

        try {
            // 导航到目标页面，增加超时时间和更好的等待策略
            const response = await this.page.goto(chapterUrl, {
                waitUntil: 'domcontentloaded',
                timeout: 60000
            });

            // 检查响应状态
            if (response.status() === 404) {
                console.log(`❌ 章节 ${chapter} 返回 404，不存在`);
                return { success: false, error: 'Chapter not found' };
            }

            if (response.status() >= 400) {
                console.log(`❌ 章节 ${chapter} 返回状态码: ${response.status()}`);
                return { success: false, error: `HTTP ${response.status()}` };
            }

            // 等待页面基本内容加载
            await this.page.waitForLoadState('domcontentloaded');

            // 验证页面是否包含漫画内容
            const hasContent = await this.verifyChapterContent();
            if (!hasContent) {
                console.log(`❌ 章节 ${chapter} 页面无有效内容`);
                return { success: false, error: 'No valid content' };
            }

            // 获取章节标题
            const title = await this.getChapterTitle();

            console.log(`✅ 成功导航到章节 ${chapter}`);
            return {
                success: true,
                title: title,
                url: chapterUrl
            };

        } catch (error) {
            console.log(`❌ 导航到章节 ${chapter} 失败: ${error.message}`);

            // 特殊处理常见错误
            if (error.message.includes('404') ||
                error.message.includes('net::ERR_HTTP_RESPONSE_CODE_FAILURE')) {
                return { success: false, error: 'Chapter not found' };
            }

            if (error.message.includes('timeout')) {
                return { success: false, error: 'Navigation timeout' };
            }

            throw error; // 重新抛出未知错误
        }
    }

    /**
     * 验证章节页面是否包含有效的漫画内容
     */
    async verifyChapterContent() {
        try {
            // 等待漫画内容容器出现
            await this.page.waitForSelector('.mh_comicpic', { timeout: 10000 });

            // 检查是否有实际的图片内容
            const contentCheck = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                return comicPics.length > 0;
            });

            return contentCheck;
        } catch (error) {
            console.log(`⚠️ 验证章节内容失败: ${error.message}`);
            return false;
        }
    }

    async analyzeChapterCompleteness(chapterDir, skipWebCheck = false) {
        // 分析章节完整性
        const localProgress = await this.analyzeChapterProgress(chapterDir);

        if (localProgress.totalExisting === 0) {
            return { isComplete: false, hasPartialContent: false, localProgress };
        }

        // 如果跳过网页检查，使用更宽松的本地判断
        if (skipWebCheck) {
            console.log(`📊 本地图片分析: ${localProgress.totalExisting} 张，缺失 ${localProgress.missingPages.length} 页`);

            // 如果本地有足够多的图片且缺失页面很少，认为基本完整
            if (localProgress.totalExisting >= 10 && localProgress.missingPages.length <= 3) {
                return { isComplete: true, hasPartialContent: false, localProgress };
            } else if (localProgress.totalExisting > 0) {
                return { isComplete: false, hasPartialContent: true, localProgress };
            } else {
                return { isComplete: false, hasPartialContent: false, localProgress };
            }
        }

        // 获取网页中的图片数量
        const webImageCount = await this.getWebImageCount();

        console.log(`📊 图片数量对比: 本地 ${localProgress.totalExisting} 张 vs 网页 ${webImageCount} 张`);

        if (localProgress.totalExisting === webImageCount && webImageCount > 0 && localProgress.missingPages.length === 0) {
            return { isComplete: true, hasPartialContent: false, localProgress, webImageCount };
        } else if (localProgress.totalExisting > 0) {
            return { isComplete: false, hasPartialContent: true, localProgress, webImageCount };
        } else {
            return { isComplete: false, hasPartialContent: false, localProgress, webImageCount };
        }
    }

    async getWebImageCount() {
        try {
            console.log('🔍 开始获取网页图片数量...');

            // 等待页面内容加载
            await this.page.waitForSelector('.mh_comicpic', { timeout: 15000 });

            // 使用优化的滚动策略，确保所有懒加载内容都被加载
            console.log('📜 执行智能滚动以触发懒加载...');
            await this.humanLikeScroll();

            // 额外等待一段时间确保所有图片请求都完成
            console.log('⏳ 等待图片加载完成...');
            await this.waitForImagesLoaded();

            // 获取网页中带有p属性的.mh_comicpic元素数量（过滤脏数据）
            const webImageResult = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                let validElementCount = 0;
                let loadedBlobCount = 0;
                let errorCount = 0;

                console.log(`🔍 检查 ${comicPics.length} 个 .mh_comicpic 元素...`);

                for (let i = 0; i < comicPics.length; i++) {
                    const pic = comicPics[i];
                    const pValue = pic.getAttribute('p');
                    const img = pic.querySelector('img');

                    // 检查是否有 .mh_loaderr 元素
                    const errorElement = pic.querySelector('.mh_loaderr');

                    if (errorElement) {
                        const errorStyle = window.getComputedStyle(errorElement);
                        const isErrorHidden = errorStyle.display === 'none';

                        if (!isErrorHidden) {
                            // 加载失败的元素，记录但不计入统计
                            errorCount++;
                            console.log(`❌ p=${pValue} 加载失败 (.mh_loaderr 可见)`);
                            continue;
                        } else if (isErrorHidden && !pValue) {
                            // 隐藏的错误元素且没有p属性，属于脏数据
                            console.log(`🗑️ 过滤脏数据: .mh_loaderr 隐藏且无p属性`);
                            continue;
                        }
                    }

                    if (pValue) {
                        validElementCount++;

                        // 同时检查图片是否已加载为blob
                        if (img && img.src && img.src.startsWith('blob:')) {
                            loadedBlobCount++;
                        }

                        console.log(`📄 p=${pValue}, img=${img ? (img.src ? img.src.substring(0, 50) + '...' : 'no src') : 'no img'}`);
                    }
                }

                console.log(`� 统计结果:`);
                console.log(`   - 带p属性的元素: ${validElementCount} 个`);
                console.log(`   - 已加载blob图片: ${loadedBlobCount} 个`);
                console.log(`   - 加载完成率: ${validElementCount > 0 ? ((loadedBlobCount / validElementCount) * 100).toFixed(1) : 0}%`);

                return {
                    validCount: validElementCount,
                    errorCount: errorCount,
                    loadedCount: loadedBlobCount
                };
            });

            // 简化处理：如果有少量加载失败，记录但不重试
            if (webImageResult.errorCount > 0) {
                console.log(`⚠️ 检测到 ${webImageResult.errorCount} 个图片加载失败，将在后续下载中处理`);
            }

            console.log(`✅ 网页图片数量获取完成: ${webImageResult.validCount} 张`);
            return webImageResult.validCount;
        } catch (error) {
            console.log(`⚠️ 获取网页图片数量时出错: ${error.message}`);
            return 0;
        }
    }

    async humanLikeScroll() {
        // 针对懒加载的智能滚动，确保滚动到底部
        console.log(`🖱️ 开始智能懒加载滚动...`);

        await this.page.evaluate(async () => {
            await new Promise((resolve) => {
                let lastComicPicCount = 0;
                let stableCount = 0;
                const stableThreshold = 2; // 减少稳定检查次数
                const checkInterval = 800; // 减少检查间隔
                const scrollDistance = 2000; // 每次滚动距离

                const checkAndScroll = () => {
                    // 获取当前页面高度信息
                    const scrollHeight = document.body.scrollHeight;
                    const currentScrollTop = window.scrollY || document.documentElement.scrollTop;
                    const windowHeight = window.innerHeight;
                    const isAtBottom = currentScrollTop + windowHeight >= scrollHeight - 100; // 允许100px误差

                    // 获取当前 .mh_comicpic 元素数量
                    const currentComicPics = document.querySelectorAll('.mh_comicpic');
                    const currentCount = currentComicPics.length;

                    console.log(`📊 当前 .mh_comicpic 数量: ${currentCount}, 滚动位置: ${currentScrollTop}/${scrollHeight}, 是否到底: ${isAtBottom}`);

                    // 检查是否需要停止滚动
                    if (currentCount === lastComicPicCount && isAtBottom) {
                        stableCount++;
                        console.log(`⏳ 数量稳定且已到底部 ${stableCount}/${stableThreshold} 次`);

                        if (stableCount >= stableThreshold) {
                            console.log(`✅ 懒加载完成，总共 ${currentCount} 个元素，已滚动到底部`);
                            resolve();
                            return;
                        }
                    } else {
                        if (currentCount !== lastComicPicCount) {
                            stableCount = 0; // 重置稳定计数
                            lastComicPicCount = currentCount;
                            console.log(`📈 数量增加到 ${currentCount}，继续滚动...`);
                        } else if (!isAtBottom) {
                            console.log(`📜 数量未变但未到底部，继续滚动...`);
                        }
                    }

                    // 向下滚动，如果接近底部则直接滚动到底部
                    if (isAtBottom) {
                        window.scrollTo(0, scrollHeight);
                        console.log(`🔽 已滚动到页面底部`);
                    } else {
                        window.scrollBy(0, scrollDistance);
                    }

                    // 继续检查
                    setTimeout(checkAndScroll, checkInterval);
                };

                // 开始第一次检查
                checkAndScroll();
            });
        });

        console.log(`✅ 智能滚动完成`);
    }

    async performIncrementalDownload(chapterDir, chapterStatus) {
        console.log(`🔄 开始增量下载，缺失页面: ${chapterStatus.localProgress.missingPages.join(', ')}`);

        // 等待图片加载完成
        await this.waitForImagesLoaded();

        // 只下载缺失的图片
        const downloadedCount = await this.downloadMissingImages(chapterDir, chapterStatus.localProgress.missingPages);

        if (downloadedCount > 0) {
            console.log(`✅ 增量下载完成，新下载 ${downloadedCount} 张图片`);

            // 验证下载完整性
            return await this.verifyAndRetryIfNeeded(chapterDir);
        } else {
            console.log(`⚠️ 增量下载未找到新图片`);
            return false;
        }
    }

    async performFullDownload(chapterDir) {
        console.log(`🆕 开始完整下载章节`);

        // 等待页面内容加载
        try {
            await this.page.waitForSelector('.mh_comicpic', { timeout: 15000 });
        } catch (error) {
            console.log(`⚠️ 没有找到图片内容`);
            return false;
        }

        // 等待图片加载完成
        await this.waitForImagesLoaded();

        // 下载所有图片
        const downloadedCount = await this.downloadPageImages(chapterDir);

        if (downloadedCount > 0) {
            console.log(`✅ 完整下载完成，共 ${downloadedCount} 张图片`);

            // 验证下载完整性
            return await this.verifyAndRetryIfNeeded(chapterDir);
        } else {
            console.log(`⚠️ 完整下载未找到图片`);
            return false;
        }
    }

    async waitForImagesLoaded() {
        console.log(`⏳ 等待图片加载完成...`);

        let attempts = 0;
        const maxAttempts = 8; // 减少最大尝试次数
        let lastLoadedCount = 0;
        let stableCount = 0;
        const stableThreshold = 2; // 减少稳定检查次数

        while (attempts < maxAttempts) {
            const loadedStatus = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                let loadedCount = 0;
                let totalWithP = 0;
                let visibleCount = 0; // 新增：统计可见的图片元素

                for (let i = 0; i < comicPics.length; i++) {
                    const pic = comicPics[i];
                    const img = pic.querySelector('img');
                    const pValue = pic.getAttribute('p');

                    if (pValue) {
                        totalWithP++;

                        // 检查元素是否在视口内或接近视口
                        const rect = pic.getBoundingClientRect();
                        const isVisible = rect.top < window.innerHeight + 1000; // 提前1000px预加载

                        if (isVisible) {
                            visibleCount++;
                        }

                        // 更宽松的加载检测：blob URL 或者有效的图片 src
                        const isLoaded = img && img.src && (
                            img.src.startsWith('blob:') ||
                            img.src.startsWith('data:') ||
                            (img.src.startsWith('http') && img.complete)
                        );

                        if (isLoaded) {
                            loadedCount++;
                        }
                    }
                }

                return {
                    loaded: loadedCount,
                    total: comicPics.length,
                    totalWithP: totalWithP,
                    visibleCount: visibleCount
                };
            });

            console.log(`⏳ 第${attempts + 1}次检查: ${loadedStatus.loaded}/${loadedStatus.totalWithP} 张图片已加载 (可见: ${loadedStatus.visibleCount}, 总元素: ${loadedStatus.total})`);

            // 更宽松的完成条件：加载数量达到总数的80%以上，或者加载数量稳定且大于可见数量的50%
            const loadingRatio = loadedStatus.totalWithP > 0 ? loadedStatus.loaded / loadedStatus.totalWithP : 0;
            const visibleRatio = loadedStatus.visibleCount > 0 ? loadedStatus.loaded / loadedStatus.visibleCount : 0;

            if (loadingRatio >= 0.8 || (loadedStatus.loaded === loadedStatus.totalWithP && loadedStatus.totalWithP > 0)) {
                console.log(`✅ 图片加载充足，继续执行 (${loadedStatus.loaded}/${loadedStatus.totalWithP}, ${(loadingRatio * 100).toFixed(1)}%)`);
                break;
            }

            // 检查加载数量是否稳定
            if (loadedStatus.loaded === lastLoadedCount) {
                stableCount++;
                console.log(`📊 加载数量稳定 ${stableCount}/${stableThreshold} 次`);

                if (stableCount >= stableThreshold && (loadedStatus.loaded > 0 || visibleRatio >= 0.5)) {
                    console.log(`⚠️ 加载数量已稳定，继续执行 (${loadedStatus.loaded}/${loadedStatus.totalWithP})`);
                    break;
                }
            } else {
                stableCount = 0;
                lastLoadedCount = loadedStatus.loaded;
            }

            await new Promise(resolve => setTimeout(resolve, 2000)); // 减少等待时间
            attempts++;
        }

        if (attempts >= maxAttempts) {
            console.log(`⚠️ 等待图片加载超时，继续执行下载`);
        }
    }

    async downloadMissingImages(chapterDir, missingPages) {
        console.log(`🔍 开始下载缺失的图片，页面: ${missingPages.join(', ')}`);

        // 获取页面上的所有图片元素信息（过滤脏数据）
        const imageInfos = await this.page.evaluate((targetPages) => {
            const comicPics = document.querySelectorAll('.mh_comicpic');
            const images = [];

            console.log(`🔍 找到 ${comicPics.length} 个 .mh_comicpic 元素`);

            for (let i = 0; i < comicPics.length; i++) {
                const pic = comicPics[i];
                const img = pic.querySelector('img');
                const pValue = pic.getAttribute('p');

                // 检查是否有 .mh_loaderr 元素
                const errorElement = pic.querySelector('.mh_loaderr');

                if (errorElement) {
                    const errorStyle = window.getComputedStyle(errorElement);
                    const isErrorHidden = errorStyle.display === 'none';

                    if (!isErrorHidden) {
                        // 加载失败的元素，跳过
                        console.log(`❌ p=${pValue} 加载失败，跳过下载`);
                        continue;
                    } else if (isErrorHidden && !pValue) {
                        // 隐藏的错误元素且没有p属性，属于脏数据
                        console.log(`🗑️ 过滤脏数据: .mh_loaderr 隐藏且无p属性`);
                        continue;
                    }
                }

                if (img && pValue) {
                    const pageNum = parseInt(pValue);
                    if (targetPages.includes(pageNum)) {
                        const src = img.src;
                        if (src && src.startsWith('blob:')) {
                            images.push({
                                blobUrl: src,
                                order: pageNum
                            });
                            console.log(`✅ 找到缺失页面的blob图片: p=${pValue}, src=${src.substring(0, 50)}...`);
                        } else {
                            console.log(`⚠️ 页面 ${pageNum} 的图片未加载或非blob URL: ${src}`);
                        }
                    }
                }
            }

            console.log(`📊 找到 ${images.length} 张缺失的blob图片`);
            return images.sort((a, b) => a.order - b.order);
        }, missingPages);

        if (imageInfos.length === 0) {
            console.log(`⚠️ 没有找到缺失页面的可下载图片`);
            return 0;
        }

        return await this.saveImages(imageInfos, chapterDir);
    }

    async verifyAndRetryIfNeeded(chapterDir, maxRetries = 1) {
        console.log(`🔍 验证下载完整性...`);

        // 先进行一次快速本地验证，避免重复的网页检查
        const chapterStatus = await this.analyzeChapterCompleteness(chapterDir, true);

        if (chapterStatus.localProgress.totalExisting === 0) {
            console.log(`❌ 没有下载到任何图片，章节下载失败`);
            return false;
        }

        // 如果本地已有较多图片，进行一次轻量级的补充下载
        if (chapterStatus.localProgress.missingPages.length > 0 && chapterStatus.localProgress.missingPages.length <= 5) {
            console.log(`🔄 发现少量缺失页面: ${chapterStatus.localProgress.missingPages.join(', ')}, 尝试补充下载`);

            // 不刷新页面，直接尝试下载缺失的图片
            const downloadedCount = await this.downloadMissingImages(chapterDir, chapterStatus.localProgress.missingPages);

            if (downloadedCount > 0) {
                console.log(`✅ 补充下载了 ${downloadedCount} 张图片`);

                // 重新检查本地进度（跳过网页检查）
                const updatedStatus = await this.analyzeChapterCompleteness(chapterDir, true);
                if (updatedStatus.isComplete) {
                    console.log(`✅ 章节下载完整，验证通过`);
                    return true;
                }
            }
        }

        // 如果缺失页面较多，才进行一次页面刷新重试
        if (chapterStatus.localProgress.missingPages.length > 5 && maxRetries > 0) {
            console.log(`⚠️ 缺失页面较多 (${chapterStatus.localProgress.missingPages.length} 页), 尝试刷新重试`);

            await this.page.reload({ waitUntil: 'domcontentloaded' });
            await new Promise(resolve => setTimeout(resolve, 2000));

            await this.humanLikeScroll();
            await this.waitForImagesLoaded();

            const downloadedCount = await this.downloadMissingImages(chapterDir, chapterStatus.localProgress.missingPages);
            console.log(`🔄 重试下载了 ${downloadedCount} 张图片`);
        }

        // 最终验证
        const finalProgress = await this.analyzeChapterProgress(chapterDir);
        const completionRate = finalProgress.totalExisting / (finalProgress.totalExisting + finalProgress.missingPages.length);

        if (completionRate >= 0.9) { // 90%以上完成率认为成功
            console.log(`✅ 章节下载基本完整 (${(completionRate * 100).toFixed(1)}%), 验证通过`);
            return true;
        } else {
            console.log(`⚠️ 章节下载不完整 (${(completionRate * 100).toFixed(1)}%), 但继续处理`);
            return true; // 改为宽松策略，避免无限重试
        }
    }

    async fastScrollToLoadElements() {
        // 快速滚动页面以确保所有DOM元素加载（不等待图片）
        await this.page.evaluate(async () => {
            await new Promise((resolve) => {
                let totalHeight = 0;
                const distance = 500; // 更大的滚动距离
                const timer = setInterval(() => {
                    const scrollHeight = document.body.scrollHeight;
                    window.scrollBy(0, distance);
                    totalHeight += distance;

                    if (totalHeight >= scrollHeight) {
                        clearInterval(timer);
                        // 只需要短暂等待DOM元素加载，不等待图片
                        setTimeout(resolve, 500);
                    }
                }, 100); // 更快的滚动间隔
            });
        });
    }

    async downloadPageImages(chapterDir) {
        console.log('🔍 开始获取页面图片信息...');

        // 获取页面上的所有图片元素信息（过滤脏数据）
        const imageInfos = await this.page.evaluate(() => {
            const comicPics = document.querySelectorAll('.mh_comicpic');
            const images = [];
            let filteredCount = 0;

            console.log(`🔍 找到 ${comicPics.length} 个 .mh_comicpic 元素`);

            for (let i = 0; i < comicPics.length; i++) {
                const pic = comicPics[i];
                const img = pic.querySelector('img');
                const pValue = pic.getAttribute('p');

                // 检查是否有 .mh_loaderr 元素
                const errorElement = pic.querySelector('.mh_loaderr');

                if (errorElement) {
                    const errorStyle = window.getComputedStyle(errorElement);
                    const isErrorHidden = errorStyle.display === 'none';

                    if (!isErrorHidden) {
                        // 加载失败的元素，跳过
                        console.log(`❌ p=${pValue} 加载失败，跳过下载`);
                        continue;
                    } else if (isErrorHidden && !pValue) {
                        // 隐藏的错误元素且没有p属性，属于脏数据
                        filteredCount++;
                        console.log(`🗑️ 过滤脏数据: .mh_loaderr 隐藏且无p属性`);
                        continue;
                    }
                }

                if (img && pValue) {
                    const src = img.src;
                    if (src && src.startsWith('blob:')) {
                        images.push({
                            blobUrl: src,
                            order: parseInt(pValue) || 0
                        });
                        console.log(`✅ 找到blob图片: p=${pValue}, src=${src.substring(0, 50)}...`);
                    } else {
                        console.log(`⚠️ p=${pValue} 非blob URL: ${src}`);
                    }
                } else if (pValue) {
                    console.log(`⚠️ p=${pValue} 没有img元素`);
                }
            }

            console.log(`📊 图片获取统计:`);
            console.log(`   - 总元素: ${comicPics.length} 个`);
            console.log(`   - 过滤脏数据: ${filteredCount} 个`);
            console.log(`   - 找到blob图片: ${images.length} 张`);

            return images.sort((a, b) => a.order - b.order);
        });

        console.log(`🖼️ 最终找到 ${imageInfos.length} 张可下载的图片`);

        if (imageInfos.length === 0) {
            console.log(`⚠️ 没有找到可下载的图片，可能的原因:`);
            console.log(`   - 图片还未加载完成`);
            console.log(`   - 所有图片都加载失败`);
            console.log(`   - 页面结构发生变化`);
            return 0;
        }

        return await this.saveImages(imageInfos, chapterDir);
    }

    async saveImages(imageInfos, chapterDir) {
        console.log(`💾 开始并行保存 ${imageInfos.length} 张图片...`);

        // 并行下载，但限制并发数量避免过载
        const concurrency = 3; // 同时下载3张图片
        let downloadedCount = 0;

        for (let i = 0; i < imageInfos.length; i += concurrency) {
            const batch = imageInfos.slice(i, i + concurrency);

            const promises = batch.map(async (imageInfo) => {
                try {
                    // 从blob URL中提取UUID
                    const uuid = this.extractUuidFromBlob(imageInfo.blobUrl);
                    const fileName = `${imageInfo.order}-${uuid}.png`;
                    const filePath = path.join(chapterDir, fileName);

                    // 检查文件是否已存在
                    if (await fs.pathExists(filePath)) {
                        console.log(`⏭️ 文件已存在，跳过: ${fileName}`);
                        return true;
                    }

                    // 使用Playwright的页面截图功能来获取图片
                    const imgSelector = `.mh_comicpic[p="${imageInfo.order}"] img`;

                    const imgElement = await this.page.$(imgSelector);
                    if (imgElement) {
                        console.log(`📸 截图元素: p=${imageInfo.order}`);
                        const buffer = await imgElement.screenshot();

                        await fs.writeFile(filePath, buffer);
                        console.log(`💾 保存图片: ${fileName} (${buffer.length} bytes)`);
                        return true;
                    } else {
                        console.error(`❌ 未找到元素: ${imgSelector}`);
                        return false;
                    }
                } catch (error) {
                    console.error(`❌ 保存图片失败 (p=${imageInfo.order}):`, error.message);
                    return false;
                }
            });

            const results = await Promise.all(promises);
            downloadedCount += results.filter(result => result).length;

            // 批次间短暂延迟
            if (i + concurrency < imageInfos.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        console.log(`✅ 并行保存完成，成功保存 ${downloadedCount}/${imageInfos.length} 张图片`);
        return downloadedCount;
    }

    extractUuidFromBlob(blobUrl) {
        // 从blob URL中提取UUID
        // 格式: blob:https://www.colamanga.com/91799778-e7d0-401c-ba8c-5d9b02672782
        const match = blobUrl.match(/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/);
        return match ? match[1] : 'unknown';
    }

    async getExistingImages(chapterDir) {
        // 获取已存在的图片文件
        if (!await fs.pathExists(chapterDir)) {
            return [];
        }

        const files = await fs.readdir(chapterDir);
        return files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));
    }

    async clearChapterDirectory(chapterDir) {
        try {
            console.log(`🗑️ 清空章节目录: ${chapterDir}`);
            await fs.emptyDir(chapterDir);
            console.log(`✅ 章节目录已清空`);
        } catch (error) {
            console.error(`❌ 清空目录失败: ${error.message}`);
        }
    }

    async analyzeChapterProgress(chapterDir) {
        // 分析章节下载进度
        if (!await fs.pathExists(chapterDir)) {
            return { existingFiles: [], missingPages: [], isComplete: false, maxPage: 0 };
        }

        const files = await fs.readdir(chapterDir);
        const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));

        // 提取页面编号
        const pageNumbers = imageFiles.map(file => {
            const match = file.match(/^(\d+)-/);
            return match ? parseInt(match[1]) : 0;
        }).filter(num => num > 0).sort((a, b) => a - b);

        const maxPage = pageNumbers.length > 0 ? Math.max(...pageNumbers) : 0;
        const missingPages = [];

        // 检查连续性
        for (let i = 1; i <= maxPage; i++) {
            if (!pageNumbers.includes(i)) {
                missingPages.push(i);
            }
        }

        return {
            existingFiles: imageFiles,
            missingPages,
            isComplete: missingPages.length === 0 && maxPage > 0,
            maxPage,
            totalExisting: pageNumbers.length
        };
    }

    async checkChapterExists(mangaId, chapter) {
        // 检查章节是否存在（不下载，只检查）
        try {
            const chapterUrl = `https://www.colamanga.com/manga-${mangaId}/${chapter}.html`;
            const response = await this.page.goto(chapterUrl, {
                waitUntil: 'domcontentloaded',
                timeout: 30000
            });

            if (response.status() === 404) {
                return false;
            }

            // 检查是否有图片内容
            try {
                await this.page.waitForSelector('.mh_comicpic', { timeout: 5000 });
                return true;
            } catch {
                return false;
            }
        } catch (error) {
            if (error.message.includes('404') || error.message.includes('net::ERR_HTTP_RESPONSE_CODE_FAILURE')) {
                return false;
            }
            throw error;
        }
    }

    sanitizeFileName(fileName) {
        // 清理文件名，移除不合法字符
        return fileName.replace(/[<>:"/\\|?*：？]/g, '_').trim();
    }

    async downloadFromMangaList(mangaListFile, startIndex = 0, count = null, maxChapters = null) {
        const mangaList = await fs.readJson(mangaListFile);
        console.log(`📚 加载漫画列表，共 ${mangaList.length} 个漫画`);

        const endIndex = count ? Math.min(startIndex + count, mangaList.length) : mangaList.length;
        console.log(`📋 将下载漫画 ${startIndex + 1} 到 ${endIndex}${maxChapters ? `，每个漫画最多 ${maxChapters} 章` : '，不限制章节数'}`);

        for (let i = startIndex; i < endIndex; i++) {
            const manga = mangaList[i];
            console.log(`\n${'='.repeat(60)}`);
            console.log(`📖 [${i + 1}/${endIndex}] 开始下载漫画: ${manga.name} (ID: ${manga.id})`);
            console.log(`${'='.repeat(60)}`);

            let totalChaptersDownloaded = 0;
            let consecutiveFailures = 0;
            const maxConsecutiveFailures = 3;

            try {
                // 如果没有指定最大章节数，则持续下载直到没有更多章节
                let chapter = 1;
                while (true) {
                    console.log(`\n📚 [章节 ${chapter}] 开始下载...`);

                    try {
                        const success = await this.downloadMangaContent(manga.id, manga.name, chapter);

                        if (success) {
                            totalChaptersDownloaded++;
                            consecutiveFailures = 0; // 重置连续失败计数
                            console.log(`✅ [章节 ${chapter}] 下载成功！已下载 ${totalChaptersDownloaded} 章`);
                        } else {
                            consecutiveFailures++;
                            console.log(`📄 [章节 ${chapter}] 不存在或下载失败 (连续失败 ${consecutiveFailures}/${maxConsecutiveFailures})`);

                            if (consecutiveFailures >= maxConsecutiveFailures) {
                                console.log(`⚠️ 连续 ${maxConsecutiveFailures} 章失败，认为后续章节不存在，停止下载`);
                                break;
                            }
                        }
                    } catch (error) {
                        consecutiveFailures++;
                        console.error(`❌ [章节 ${chapter}] 下载异常:`, error.message);

                        // 如果是404错误，直接停止
                        if (error.message.includes('404') || error.message.includes('net::ERR_HTTP_RESPONSE_CODE_FAILURE')) {
                            console.log(`📄 [章节 ${chapter}] 确认不存在，停止下载后续章节`);
                            break;
                        }

                        // 连续失败太多次则停止
                        if (consecutiveFailures >= maxConsecutiveFailures) {
                            console.log(`⚠️ 连续 ${maxConsecutiveFailures} 章异常，停止下载`);
                            break;
                        }
                    }

                    chapter++;

                    // 如果指定了最大章节数，检查是否达到限制
                    if (maxChapters && chapter > maxChapters) {
                        console.log(`📚 已达到最大章节数限制 ${maxChapters}，停止下载`);
                        break;
                    }

                    // 章节间延时
                    console.log(`⏳ 等待 2 秒后继续下载下一章...`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }

                console.log(`\n📊 漫画 "${manga.name}" 下载完成，共成功下载 ${totalChaptersDownloaded} 章`);

            } catch (error) {
                console.error(`❌ 下载漫画 "${manga.name}" 时发生严重错误:`, error.message);
                console.error(`📊 该漫画已成功下载 ${totalChaptersDownloaded} 章`);
            }

            // 漫画间延时
            if (i < endIndex - 1) { // 不是最后一个漫画
                console.log(`\n⏳ 等待 3 秒后开始下载下一个漫画...`);
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        console.log(`\n🎉 批量下载完成！共处理 ${endIndex - startIndex} 个漫画`);
    }

    async close() {
        if (this.context) {
            await this.context.close();
            console.log('🔒 浏览器已关闭');
        }
    }
}

// 测试新的下载逻辑
async function testNewDownloadLogic() {
    const downloader = new MangaContentDownloader();

    try {
        await downloader.init();

        // 测试单个章节下载
        console.log('🧪 测试新的下载逻辑...');
        const success = await downloader.downloadMangaContent('ap101511', '测试漫画', 1);

        if (success) {
            console.log('✅ 新下载逻辑测试成功！');
        } else {
            console.log('❌ 新下载逻辑测试失败！');
        }

    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 主函数
async function main() {
    const downloader = new MangaContentDownloader();

    try {
        await downloader.init();

        // 示例：下载单个漫画
        // await downloader.downloadMangaContent('ap101511', '示例漫画', 1);

        // 示例：从漫画列表文件批量下载
        const mangaListFile = path.join('./manga-ids.json');
        if (await fs.pathExists(mangaListFile)) {
            // 下载前5个漫画，不限制章节数（移除第4个参数）
            await downloader.downloadFromMangaList(mangaListFile, 0, 5);
        } else {
            console.log('❌ 未找到漫画列表文件，请先运行 collect-manga-ids.js');
        }

    } catch (error) {
        console.error('❌ 下载过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = MangaContentDownloader;




